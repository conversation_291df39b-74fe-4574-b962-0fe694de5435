using Azure;
using Azure.Search.Documents;
using Azure.Search.Documents.Models;

namespace Avanade.Geranium.Attane.Api.Controllers
{
    /// <summary>
    /// Azure OpenAI APIにリクエストを送り、生成されたクエリを返却
    /// </summary>
    [Route("openai")]
    [Produces(System.Net.Mime.MediaTypeNames.Application.Json)]
    public class OpenAIController : ControllerBase
    {
        private readonly ILogger<OpenAIController> _logger;
        /// <summary>
        /// コンフィグレーションからAPIキーとエンドポイントを取得
        /// </summary>
        // private readonly string? _apiKey;
        // private readonly string? _endpoint;
        // private readonly string? _deploymentName;
        private readonly string? _searchApiKey;
        private readonly string? _searchServiceName;
        private readonly string? _mailindexName;
        private readonly string? _mailsemanticConfigurationName;
        private readonly string? _spoindexName;
        private readonly string? _sposemanticConfigurationName;
        private readonly string? _chatindexName;
        private readonly string? _chatsemanticConfigurationName;

        /// <summary>
        /// OpenAIControllerのコンストラクタ
        /// </summary>
        /// <param name="configuration">アプリケーションの設定情報</param>
        /// <param name="logger">ロガー</param>
        public OpenAIController(IConfiguration configuration, ILogger<OpenAIController> logger)
        {
            _logger = logger;
            // OpenAI環境変数
            // _apiKey = configuration["OpenAI:ApiKey"];
            // _endpoint = configuration["OpenAI:Endpoint"];
            // _deploymentName = configuration["OpenAI:DeploymentName"];
            // Index共通環境変数
            _searchServiceName = configuration["AISearch:ServiceName"];
            _searchApiKey = configuration["AISearch:ApiKey"];
            // MailIndex環境変数
            _mailindexName = configuration["AISearch:Mail:0:MailIndex"];
            _mailsemanticConfigurationName = configuration["AISearch:Mail:0:MailSemanticConfiguration"];
            // SPOIndex環境変数
            _spoindexName = configuration["AISearch:SPO:0:SPOIndex"];
            _sposemanticConfigurationName = configuration["AISearch:SPO:0:SPOSemanticConfiguration"];
            // ChatIndex環境変数
            _chatindexName = configuration["AISearch:Chat:0:ChatIndex"];
            _chatsemanticConfigurationName = configuration["AISearch:Chat:0:ChatSemanticConfiguration"];

        }

        /// <summary>
        /// クエリ生成リクエストのモデル
        /// </summary>
        public new class Request
        {
            /// <summary>
            /// 入力テキスト
            /// </summary>
            public string? Input { get; set; }
            /// <summary>
            /// ユーザーID
            /// </summary>
            public string? UserId { get; set; }
            /// <summary>
            /// グループID
            /// </summary>
            public List<string>? GroupIds { get; set; }
            /// <summary>
            /// 日付フィルターFrom
            /// </summary>
            public string? From { get; set; }
            /// <summary>
            /// 日付フィルターTo
            /// </summary>
            public string? To { get; set; }
            /// <summary>
            /// 検索対象フィルター
            /// </summary>
            public string? SourceFilter { get; set; }
        }
        /// <summary>
        /// 指定された入力に基づいてAzure OpenAI APIから検索キーワードを生成します。
        /// </summary>
        /// <param name="request">検索クエリのための入力文字列</param>
        /// <returns>生成された検索クエリ</returns>
        [HttpPost("aisearch")]
        public async Task<IActionResult> SearchResults([FromBody] Request request)
        {
            var startTime = DateTime.UtcNow;
            _logger.LogInformation("Search request started for user {UserId}", request?.UserId);

            try
            {
                // requestの中身、環境変数がなければエラー
                if (request == null)
                {
                    _logger.LogError("Request is null");
                    return BadRequest(new { error = "Request cannot be null." });
                }
                if (string.IsNullOrEmpty(request.Input))
                {
                    _logger.LogError("Input is empty for user {UserId}", request.UserId);
                    return BadRequest(new { error = "Input cannot be empty." });
                }
                if (string.IsNullOrEmpty(request.UserId))
                {
                    _logger.LogError("UserId is empty");
                    return BadRequest(new { error = "UserId cannot be empty." });
                }
                if (request.GroupIds == null || request.GroupIds.Count == 0)
                {
                    _logger.LogError("GroupIds is null or empty for user {UserId}", request.UserId);
                    return BadRequest(new { error = "GroupIds cannot be null or empty." });
                }
                // if (string.IsNullOrEmpty(_endpoint))
                // {
                //     _logger.LogError("OpenAI endpoint is not configured");
                //     return StatusCode(500, new { error = "OpenAI endpoint is not configured." });
                // }
                // if (string.IsNullOrEmpty(_apiKey))
                // {
                //     _logger.LogError("OpenAI API key is not configured");
                //     return StatusCode(500, new { error = "OpenAI API key is not configured." });
                // }

                // 検索結果を格納するリスト
                List<SearchResult<SearchDocument>> MailSearchResult = new();
                List<SearchResult<SearchDocument>> SPOSearchResult = new();
                List<SearchResult<SearchDocument>> ChatSearchResult = new();
                // エラーをまとめるリスト
                var searchErrors = new List<string>();
                // それぞれ検索が成功したか監視する
                bool mailOk = false, spoOk = false, chatOk = false;

                // MailIndexで検索
                if (!string.IsNullOrEmpty(_mailindexName) && !string.IsNullOrEmpty(_mailsemanticConfigurationName))
                {
                    try
                    {
                        _logger.LogTrace("Starting mail search with index {MailIndex}", _mailindexName);
                        MailSearchResult = await MailSearch(request.Input, request.UserId, request.From, request.To, request.SourceFilter);
                        mailOk = true; // 例外がなければ成功（0件でも成功）
                        _logger.LogTrace("Mail search completed with {Count} results", MailSearchResult.Count);
                    }
                    catch (Exception ex)
                    {
                        // Index名が間違っている場合もこちらに
                        _logger.LogError(ex, "Mail search error occurred for user {UserId},Please check exception message", request.UserId);
                        searchErrors.Add($"Mail search error: {ex.Message}");
                    }
                }
                else
                {
                    // Index名がない場合のエラー
                    _logger.LogError("Mail search skipped due to missing config.");
                }

                // SPOIndexで検索
                if (!string.IsNullOrEmpty(_spoindexName) && !string.IsNullOrEmpty(_sposemanticConfigurationName))
                {
                    try
                    {
                        _logger.LogTrace("Starting SPO search with index {SPOIndex}", _spoindexName);
                        SPOSearchResult = await SPOSearch(request.Input, request.GroupIds ?? new List<string>(), request.From, request.To, request.SourceFilter);
                        spoOk = true;
                        _logger.LogTrace("SPO search completed with {Count} results", SPOSearchResult.Count);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "SPO search error occurred for groups {GroupIds},Please check exception message", string.Join(", ", request.GroupIds ?? new List<string>()));
                        searchErrors.Add($"SPO search error: {ex.Message}");
                    }
                }
                else
                {
                    _logger.LogError("SPO search skipped due to missing config.");
                }

                // ChatIndexで検索
                if (!string.IsNullOrEmpty(_chatindexName) && !string.IsNullOrEmpty(_chatsemanticConfigurationName))
                {
                    try
                    {
                        _logger.LogTrace("Starting chat search with index {ChatIndex}", _chatindexName);
                        ChatSearchResult = await ChatSearch(request.Input, request.UserId, request.From, request.To, request.SourceFilter);
                        chatOk = true;
                        _logger.LogTrace("Chat search completed with {Count} results", ChatSearchResult.Count);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Chat search error occurred for user {UserId},Please check exception message", request.UserId);
                        searchErrors.Add($"Chat search error: {ex.Message}");
                    }
                }
                else
                {
                    _logger.LogError("Chat search skipped due to missing config.");
                }
                // 全ての検索が「失敗（= 例外 or 未設定で一度も成功していない）」なら 500
                if (!mailOk && !spoOk && !chatOk)
                {
                    _logger.LogError("All search operations failed for user {UserId}. Errors: {Errors}",
                        request.UserId, string.Join("; ", searchErrors));
                    return StatusCode(500, new { error = "All search operations failed.", details = searchErrors });
                }

                // ここからは成功ソースが1つでもある（0件でもOK）
                var allResults = MailSearchResult.Concat(SPOSearchResult).Concat(ChatSearchResult);
                // 結果が0件でも 200 を返す（要件どおり）。部分エラーは同梱。
                if (!allResults.Any())
                {
                    _logger.LogInformation("No search results found for user {UserId}", request.UserId);
                    return Ok(new
                    {
                        MergedResult = new List<SearchResult<SearchDocument>>(),
                    });
                }
                // 重複idを削除、rerankerScoreが高い順にソートし、上位50件を取得（nullガード付き）
                var MergedResult = allResults
                    .Select(r => new
                    {
                        Result = r,
                        Id = r.Document.TryGetValue("id", out var idObj) ? idObj?.ToString() : null,
                        Score = r.SemanticSearch?.RerankerScore ?? 0.0
                    })
                    .Where(x => !string.IsNullOrEmpty(x.Id))
                    .GroupBy(x => x.Id!)
                    .Select(g => g.OrderByDescending(x => x.Score).First().Result)
                    .OrderByDescending(r => r.SemanticSearch?.RerankerScore ?? 0.0)
                    .Take(50)
                    .ToList();
                // 要約機能をonにするときにコメントアウト外す
                // var topResults = MergedResult
                //     // トークン制限の問題があるため10件のみとしている
                //     .Take(10) // 上位10件を取得
                //     .Select(result => result.Document["chunk"]) // chunkのみを取得することでトークンを削減
                //     .ToList();
                // TODO:ここJson化しようとするとHTMLがありエラーが起きているので修正
                // var serializedTopResults = JsonSerializer.Serialize(topResults);

                // try
                // {
                //     var openAIClient = new AzureOpenAIClient(new Uri(_endpoint), new AzureKeyCredential(_apiKey));
                //     var chatClient = openAIClient.GetChatClient(_deploymentName);

                //     var messages = new ChatMessage[]
                //     {
                //         new SystemChatMessage(@"
                //             あなたは以下のデータ(複数の記事本文)からのみ回答を生成するアシスタントです。以下の指示に従ってください。
                //             1. 質問に対して、提供された記事の本文（JSONとして与えられる）からのみ根拠を得て要約を行ってください。
                //             2. ユーザーの質問内容に直接関連する部分のみを、記事間の重複や冗長を避けて簡潔に要約してください。
                //             3. 質問に関係のない情報は一切含めないでください。
                //             4. 明確な回答が見つからない場合でも「データには回答が見つかりませんでした」とは言わず、全体の要約を必ず行ってください。
                //             以上を踏まえて、以下に与えるデータをもとに要約を行ってください。
                //             "),
                //         new UserChatMessage(serializedTopResults)
                //     };
                //     ChatCompletionOptions chatCompletionsOptions = new ChatCompletionOptions
                //     {
                //         Temperature = 0.1f
                //     };

                var duration = DateTime.UtcNow - startTime;
                _logger.LogInformation("Search request completed in {Duration}ms for user {UserId}", duration.TotalMilliseconds, request.UserId);
                return Ok(new
                {
                    MergedResult,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in search request for user {UserId}", request?.UserId);
                return StatusCode(500, new { error = $"Unexpected error: {ex.Message}" });
            }
        }
        private async Task<List<SearchResult<SearchDocument>>> MailSearch(
        string query, string userId, string? from, string? to, string? sourceFilter)
        {
            var startTime = DateTime.UtcNow;
            _logger.LogTrace("Mail search started for user {UserId}", userId);
            try
            {
                // エンドポイント設定
                var endpoint = new Uri($"https://{_searchServiceName}.search.windows.net");
                if (string.IsNullOrEmpty(_searchApiKey))
                {
                    throw new InvalidOperationException("Search API key cannot be null or empty.");
                }

                var credential = new AzureKeyCredential(_searchApiKey);

                // クライアント作成
                var searchClient = new SearchClient(endpoint, _mailindexName, credential);

                // フィルター作成
                var filterClauses = new List<string>
        {
            $"security_user_id/any(id: id eq '{userId}')"
        };

                if (!string.IsNullOrEmpty(from))
                {
                    var fromDate = DateTime.Parse(from).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
                    filterClauses.Add($"(receivedDateTime2 ge '{fromDate}')");
                }
                if (!string.IsNullOrEmpty(to))
                {
                    var tp = DateTime.Parse(to);
                    var toUtc = (to.Contains('T') ? tp : tp.Date.AddDays(1)).ToUniversalTime();
                    var op = to.Contains('T') ? "le" : "lt";
                    filterClauses.Add($"(receivedDateTime2 {op} '{toUtc:yyyy-MM-ddTHH:mm:ssZ}')");
                }
                if (!string.IsNullOrEmpty(sourceFilter))
                {
                    var kinds = sourceFilter
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(k => k.Trim())
                        .Where(k => k.Length > 0)
                        .Select(k => k.Replace("'", "''"))
                        .Distinct(StringComparer.OrdinalIgnoreCase)
                        .ToArray();

                    if (kinds.Length == 1)
                    {
                        filterClauses.Add($"(kind eq '{kinds[0]}')");
                    }
                    else if (kinds.Length > 1)
                    {
                        var orExpr = string.Join(" or ", kinds.Select(k => $"kind eq '{k}'"));
                        filterClauses.Add($"({orExpr})");
                    }
                }
                var combinedFilter = string.Join(" and ", filterClauses);
                _logger.LogTrace("Mail search filter conditions: {Filter}", combinedFilter);

                // SearchOptions
                var options = new SearchOptions
                {
                    SearchFields = { "subject", "chunk" },
                    VectorSearch = new()
                    {
                        Queries = {
                    new VectorizableTextQuery(query)
                    {
                        Fields = { "text_vector" },
                    }
                }
                    },
                    Filter = combinedFilter,
                    QueryType = SearchQueryType.Semantic,
                    SemanticSearch = new()
                    {
                        SemanticConfigurationName = _mailsemanticConfigurationName,
                    },
                };

                // 検索実行
                var searchResults = await searchClient.SearchAsync<SearchDocument>(query, options);

                // 結果取得
                var resultList = new List<SearchResult<SearchDocument>>();
                await foreach (var result in searchResults.Value.GetResultsAsync())
                {
                    resultList.Add(result);
                }

                var duration = DateTime.UtcNow - startTime;
                _logger.LogTrace("Mail search completed in {Duration}ms for user {UserId}", duration.TotalMilliseconds, userId);
                return resultList;
            }
            catch (RequestFailedException ex)
            {
                _logger.LogError(ex, "[EmbeddingAPIError] userId: {UserId} | query: {Query} | Status: {Status} | ErrorCode: {ErrorCode} | Message: {Message}",
                    userId,
                    query,
                    ex.Status,
                    ex.ErrorCode,
                    ex.Message
                );
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "Mail search failed for user {UserId}", userId);
                throw;
            }
        }

        private async Task<List<SearchResult<SearchDocument>>> SPOSearch(string query, List<string> groupIds, string? from, string? to, string? sourceFilter)
        {
            var startTime = DateTime.UtcNow;
            _logger.LogTrace("SPO search started for groups {GroupIds}", string.Join(", ", groupIds));

            try
            {
                var endpoint = new Uri($"https://{_searchServiceName}.search.windows.net");

                if (string.IsNullOrEmpty(_searchApiKey))
                {
                    throw new InvalidOperationException("Search API key cannot be null or empty.");
                }
                var credential = new AzureKeyCredential(_searchApiKey);

                var searchClient = new SearchClient(endpoint, _spoindexName, credential);

                var filterClauses = new List<string>();
                // --- グループIDのフィルター生成 ---
                if (groupIds != null && groupIds.Count > 0)
                {
                    var securityFilter = "";
                    if (groupIds.Count == 1)
                    {
                        securityFilter = $"security_user_id/any(id: id eq '{groupIds[0]}')";
                    }
                    else // 2件以上
                    {
                        var orFilter = string.Join(" or ", groupIds.Select(id => $"id eq '{id}'"));
                        securityFilter = $"security_user_id/any(id: {orFilter})";
                    }
                    filterClauses.Add(securityFilter);
                }
                if (!string.IsNullOrEmpty(from))
                {
                    var fromDate = DateTime.Parse(from).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
                    filterClauses.Add($"(properties/updatedDate ge '{fromDate}')");
                }
                if (!string.IsNullOrEmpty(to))
                {
                    var tp = DateTime.Parse(to);
                    var toUtc = (to.Contains('T') ? tp : tp.Date.AddDays(1)).ToUniversalTime();
                    var op = to.Contains('T') ? "le" : "lt";
                    filterClauses.Add($"(properties/updatedDate {op} '{toUtc:yyyy-MM-ddTHH:mm:ssZ}')");
                }
                if (!string.IsNullOrEmpty(sourceFilter))
                {
                    var kinds = sourceFilter
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(k => k.Trim())
                        .Where(k => k.Length > 0)
                        .Select(k => k.Replace("'", "''"))
                        .Distinct(StringComparer.OrdinalIgnoreCase)
                        .ToArray();

                    if (kinds.Length == 1)
                    {
                        filterClauses.Add($"(kind eq '{kinds[0]}')");
                    }
                    else if (kinds.Length > 1)
                    {
                        var orExpr = string.Join(" or ", kinds.Select(k => $"kind eq '{k}'"));
                        filterClauses.Add($"({orExpr})");
                    }
                }
                var combinedFilter = string.Join(" and ", filterClauses);
                _logger.LogTrace("SPO search filter conditions: {Filter}", combinedFilter);
                var options = new SearchOptions
                {
                    SearchFields = {
                        "chunk",        // 本文
                         },
                    VectorSearch = new()
                    {
                        Queries = {
                    new VectorizableTextQuery(query) {
                        Fields = { "text_vector" },
                    }
                }
                    },

                    Filter = combinedFilter,

                    QueryType = SearchQueryType.Semantic,
                    SemanticSearch = new()
                    {
                        SemanticConfigurationName = _sposemanticConfigurationName,
                    },
                    Size = 50,
                };

                var searchResults = await searchClient.SearchAsync<SearchDocument>(
                    query,
                    options
                );

                var resultList = new List<SearchResult<SearchDocument>>();

                // 非同期に結果を取得する
                await foreach (var result in searchResults.Value.GetResultsAsync())
                {
                    resultList.Add(result);
                }
                var duration = DateTime.UtcNow - startTime;
                _logger.LogTrace("SPO search completed in {Duration}ms for groups {GroupIds}", duration.TotalMilliseconds, string.Join(", ", groupIds));
                return resultList;
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "SPO search failed for groups {GroupIds}", string.Join(", ", groupIds));
                throw;
            }
        }
        private async Task<List<SearchResult<SearchDocument>>> ChatSearch(
            string query, string userId, string? from, string? to, string? sourceFilter)
        {
            var startTime = DateTime.UtcNow;
            _logger.LogTrace("Chat search started for user {UserId}", userId);
            try
            {
                // エンドポイント設定
                var endpoint = new Uri($"https://{_searchServiceName}.search.windows.net");
                if (string.IsNullOrEmpty(_searchApiKey))
                {
                    throw new InvalidOperationException("Search API key cannot be null or empty.");
                }

                var credential = new AzureKeyCredential(_searchApiKey);

                // クライアント作成
                var searchClient = new SearchClient(endpoint, _chatindexName, credential);

                // フィルター作成
                var filterClauses = new List<string>
                {
                    $"security_user_id/any(id: id eq '{userId}')"
                };

                if (!string.IsNullOrEmpty(from))
                {
                    var fromDate = DateTime.Parse(from).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ");
                    filterClauses.Add($"(createdDateTime2 ge '{fromDate}')");
                }
                if (!string.IsNullOrEmpty(to))
                {
                    var tp = DateTime.Parse(to);
                    var toUtc = (to.Contains('T') ? tp : tp.Date.AddDays(1)).ToUniversalTime();
                    var op = to.Contains('T') ? "le" : "lt";
                    filterClauses.Add($"(createdDateTime2 {op} '{toUtc:yyyy-MM-ddTHH:mm:ssZ}')");
                }
                if (!string.IsNullOrEmpty(sourceFilter))
                {
                    var kinds = sourceFilter
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(k => k.Trim())
                        .Where(k => k.Length > 0)
                        .Select(k => k.Replace("'", "''"))
                        .Distinct(StringComparer.OrdinalIgnoreCase)
                        .ToArray();

                    if (kinds.Length == 1)
                    {
                        filterClauses.Add($"(kind eq '{kinds[0]}')");
                    }
                    else if (kinds.Length > 1)
                    {
                        var orExpr = string.Join(" or ", kinds.Select(k => $"kind eq '{k}'"));
                        filterClauses.Add($"({orExpr})");
                    }
                }

                var combinedFilter = string.Join(" and ", filterClauses);
                _logger.LogTrace("Chat search filter conditions: {Filter}", combinedFilter);

                // SearchOptions
                var options = new SearchOptions
                {
                    SearchFields = { "chunk" },
                    VectorSearch = new()
                    {
                        Queries = {
                            new VectorizableTextQuery(query)
                            {
                                Fields = { "text_vector" },
                            }
                        }
                    },
                    Filter = combinedFilter,
                    QueryType = SearchQueryType.Semantic,
                    SemanticSearch = new()
                    {
                        SemanticConfigurationName = _chatsemanticConfigurationName,
                    },
                    Size = 50,
                };

                // 検索実行
                var searchResults = await searchClient.SearchAsync<SearchDocument>(query, options);

                // 結果取得
                var resultList = new List<SearchResult<SearchDocument>>();
                await foreach (var result in searchResults.Value.GetResultsAsync())
                {
                    resultList.Add(result);
                }

                var duration = DateTime.UtcNow - startTime;
                _logger.LogTrace("Chat search completed in {Duration}ms for user {UserId}", duration.TotalMilliseconds, userId);
                return resultList;
            }
            catch (RequestFailedException ex)
            {
                _logger.LogError(ex, "[EmbeddingAPIError] userId: {UserId} | query: {Query} | Status: {Status} | ErrorCode: {ErrorCode} | Message: {Message}",
                    userId,
                    query,
                    ex.Status,
                    ex.ErrorCode,
                    ex.Message
                );
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogTrace(ex, "Chat search failed for user {UserId}", userId);
                throw;
            }
        }
    }
}
