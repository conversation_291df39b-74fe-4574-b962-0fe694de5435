import { createGraphClient } from "../utilities/graph";
import { logger } from "../utilities/log";
import {
  createCredential,
  processUsersChatsDataBatch
} from "./impl";
import { validateTableStorageConnection, fetchUsersChatsData } from "../utilities/tableStorage";
import { validateCosmosDBConnection } from "../utilities/cosmos";
import {
  IUsersChatsTableStorageData,
  IModifiedUsersChatsData,
} from '../utilities/models';
import * as dotenv from "dotenv";

dotenv.config();

async function main() {
  logger.log("========== PHASE 1 ==========");
  logger.log("--- Creating Graph Client ---");
  const credential = createCredential(
    process.env["AzureTenantId"] ?? "",
    process.env["AzureClientId"] ?? "",
    process.env["AzureClientSecret"] ?? ""
  );
  if (!credential) {
    logger.error("[Index:CreateCredential] NO_CREDENTIALS");
    return Promise.reject(new Error("[Index:CreateCredential] NO_CREDENTIALS"));
  }
  const graphClient = createGraphClient(credential);

  logger.log("========== PHASE 2 ==========");
  logger.log("--- Validate Table Storage Connection ---");
  try {
    await validateTableStorageConnection(logger);
    logger.log("[Index:TableStorageConnection] Successfully Connected to Table Storage.");
  } catch (error) {
    logger.log(`[Index:TableStorageConnection] TableStorage Connection Failed: ${error}`);
    throw error;
  }

  logger.log("========== PHASE 3 ==========");
  logger.log("--- Get Table Storage Users Chats ---");

  // Read batch configuration from .env
  const CHAT_TABLE_STORAGE_BATCH_NUMBER = parseInt(process.env.CHAT_TABLE_STORAGE_BATCH_NUMBER || '2');
  const CHAT_TABLE_STORAGE_BATCH_SIZE = parseInt(process.env.CHAT_TABLE_STORAGE_BATCH_SIZE || '2');
  logger.log(`[Index:TableStorage] Running Batch ${CHAT_TABLE_STORAGE_BATCH_NUMBER} with Size ${CHAT_TABLE_STORAGE_BATCH_SIZE}`);

  let usersChatsTableStorageData: IUsersChatsTableStorageData[] = [];
  let modifiedUsersChatsTableStorageData: IModifiedUsersChatsData[] = [];
  try {
    const usersChatsTableStorageResults = await fetchUsersChatsData(logger);
    logger.log(`[Index:TableStorage] Successfully Retrieved ${usersChatsTableStorageResults.length} Users Chats Data from Table Storage.`);

    const allUsersChatsData = usersChatsTableStorageResults.map((data, index) => ({
      countId: index + 1, 
      userId: data.partitionKey,
      chatId: data.chatId,
      chatType: data.chatType,
      status: data.status
    }));

    // CHAT_TABLE_STORAGE_BATCH_NUMBER = 1
    // CHAT_TABLE_STORAGE_BATCH_SIZE = 100

    // startRange= (1 - 1) * 100 + 1
                    // = 0 * 100 + 1
                    // = 0 + 1 
                    // = 1
    const startRange = (CHAT_TABLE_STORAGE_BATCH_NUMBER - 1) * CHAT_TABLE_STORAGE_BATCH_SIZE + 1;
    // endRange = 1 * 100 = 100
    const endRange = CHAT_TABLE_STORAGE_BATCH_NUMBER * CHAT_TABLE_STORAGE_BATCH_SIZE;
    
    usersChatsTableStorageData = allUsersChatsData.filter(chat => 
      chat.countId >= startRange && chat.countId <= endRange
    );
    
    logger.log(`[Index:TableStorage] Filtered for Batch: ${CHAT_TABLE_STORAGE_BATCH_NUMBER} | Batch Size: ${startRange}-${endRange} (${usersChatsTableStorageData.length} records)`);

    // if (usersChatsTableStorageData.length > 0) {
    //     logger.log(`[Index:TableStorage] usersChatsTableStorageData: ${JSON.stringify(usersChatsTableStorageData)}`); // !!!
    // }

    // Adjust Filter
    const filterUsersChatsData = usersChatsTableStorageData.filter(chat => 
        chat.chatType === 'oneOnOne' 
        || chat.chatType === 'group'
        || chat.chatType === 'meeting'
    );
    logger.log(`[Index:TableStorage] Filtered ${filterUsersChatsData.length} Records from usersChatsTableStorageData: ${usersChatsTableStorageData.length} Total Records`);

    const chatUsersMap = new Map<string, any[]>();
    for (const chatDetail of filterUsersChatsData) {
      const chatIdVal = chatDetail.chatId;
      if (chatIdVal) {
        if (!chatUsersMap.has(chatIdVal)) {
          chatUsersMap.set(chatIdVal, []);
        }
        chatUsersMap.get(chatIdVal)!.push(chatDetail);
      }
    }

    // Create modifiedUsersChatsTableStorageData
    modifiedUsersChatsTableStorageData = Array.from(chatUsersMap.entries()).map(([chatId, users], index) => ({
      countId: index + 1,
      chatId: chatId,
      chatType: users[0].chatType,
      users: users.map(user => ({
        id: user.userId,
        status: user.status
      })),
      userCount: users.length
    }));

    // logger.log(`[Index:fetchUsersChatsData] modifiedUsersChatsTableStorageData: ${JSON.stringify(modifiedUsersChatsTableStorageData)}`); // !!!
    logger.log(`[Index:TableStorage] Converted ${usersChatsTableStorageData.length} usersChatsTableStorageData into ${modifiedUsersChatsTableStorageData.length} modifiedUsersChatsTableStorageData`);

  } catch (error) {
    logger.log(`[Index:TableStorage] Failed to Retrieved Pending Chats: ${error}`);
    throw error;
  }

  logger.log("\n========== PHASE 4 ==========");
  logger.log("--- Validate CosmosDB Connection ---");
  try {
    await validateCosmosDBConnection(logger);
    logger.log("[Index:CosmosDBConnection] Successfully Connected to CosmosDB.");
  } catch (error) {
    logger.log(`[Index:CosmosDBConnection] CosmosDB Connection Failed: ${error}`);
    throw error;
  }
  
  logger.log('========== PHASE 5 ==========');
  logger.log("--- Process Users Chats ---");
  try {
    await processUsersChatsDataBatch(logger, graphClient, modifiedUsersChatsTableStorageData);
    logger.log("[Index:ProcessUsersChatsBatch] Successfully Processed Users Chats Messages.");
  } catch (error) {
    logger.error(error);
  }

  logger.log("\n========== PHASE 6 ==========");
  logger.log("--- Finish ---");
  logger.log("[Index] All Phase Successfully Completed.");
}

main().catch((error) => {
  logger.error("Error Running Task:", error);
});